from typing import Iterable, List, Optional, Tuple

# Thin wrapper around the semantic-text-splitter Python bindings.
# Provides helper functions to create splitters for token-based, markdown-aware,
# and character-range chunking strategies.
#
# Usage examples:
#   from backend.app.services.document_parser import DocumentParser
#   parser = DocumentParser.from_tiktoken_model("gpt-3.5-turbo", max_tokens=1000)
#   chunks = parser.split_text(long_text)
#
# This module intentionally exposes a simple, testable surface. It does not
# perform I/O (file or DB access) so it can be unit-tested easily and used
# inside Dramatiq workers or FastAPI endpoints.

try:
    from semantic_text_splitter import TextSplitter, MarkdownSplitter
except Exception as e:
    # If bindings are not installed yet, raise a clear error when used.
    raise ImportError(
        "semantic-text-splitter is required. Install it in the backend virtualenv: "
        "`uv sync` then `uv run pip install semantic-text-splitter` or add to pyproject and run `uv sync`."
    ) from e


class DocumentParser:
    """
    DocumentPars<PERSON> wraps semantic-text-splitter to provide:
      - token-aware splitting (via Tiktoken model names)
      - markdown-aware splitting
      - character-range splitting

    It provides small convenience helpers returning a list of chunks (strings).
    """

    def __init__(self, splitter: TextSplitter):
        self._splitter = splitter

    @classmethod
    def from_tiktoken_model(cls, model_name: str, max_tokens: int = 1000) -> "DocumentParser":
        """
        Initialize a splitter using a tiktoken-compatible model name.

        model_name: tokenization model name, e.g. "gpt-3.5-turbo" or "gpt-4o"
        max_tokens: maximum tokens per chunk
        """
        splitter = TextSplitter.from_tiktoken_model(model_name, max_tokens)
        return cls(splitter)

    @classmethod
    def markdown_splitter(cls, max_characters: int = 1000) -> "DocumentParser":
        """
        Initialize a Markdown-aware splitter (uses MarkdownSplitter internally).
        max_characters can be an int or a (min,max) tuple according to the binder.
        """
        md_splitter = MarkdownSplitter(max_characters)
        return cls(md_splitter)

    @classmethod
    def char_range_splitter(cls, char_range: Tuple[int, int] = (200, 1000)) -> "DocumentParser":
        """
        Initialize a simple character-range splitter.
        char_range: (min_chars, max_chars) or an int for exact max.
        """
        splitter = TextSplitter(char_range)
        return cls(splitter)

    def split_text(self, text: str) -> List[str]:
        """
        Split plain text into chunks using the configured splitter.
        Returns a list of chunk strings.
        """
        return list(self._splitter.chunks(text))

    def split_documents(self, docs: Iterable[str]) -> List[str]:
        """
        Split multiple documents and return a flattened list of chunks.
        """
        chunks: List[str] = []
        for d in docs:
            chunks.extend(self.split_text(d))
        return chunks

    def join_preview(self, chunk: str, max_preview: int = 120) -> str:
        """
        Produce a short preview for logging/inspect purposes.
        """
        s = " ".join(chunk.split())
        if len(s) <= max_preview:
            return s
        return s[: max_preview - 1] + "…"


# Lightweight example helper for use in tasks/workers
def parse_and_prepare_chunks(text: str, *, model: Optional[str] = "gpt-3.5-turbo", max_tokens: int = 1000) -> List[dict]:
    """
    Convenience function used by workers:
      - splits text using a tiktoken-aware splitter
      - returns list of dicts with metadata that can be further processed (vectorize, store)
    """
    parser = DocumentParser.from_tiktoken_model(model, max_tokens)
    raw_chunks = parser.split_text(text)
    prepared = []
    for i, c in enumerate(raw_chunks):
        prepared.append(
            {
                "chunk_id": i,
                "text": c,
                "preview": parser.join_preview(c),
            }
        )
    return prepared