"""
Text-Splitter Engine 数据模型
"""

from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class ChunkType(str, Enum):
    """文本块类型"""
    TEXT = "text"
    MARKDOWN = "markdown"
    CODE = "code"
    HEADER = "header"
    LIST = "list"
    TABLE = "table"

class TextChunk(BaseModel):
    """文本块模型"""
    content: str = Field(..., description="文本块内容")
    chunk_index: int = Field(..., description="块索引")
    start_char: int = Field(..., description="起始字符位置")
    end_char: int = Field(..., description="结束字符位置")
    token_count: Optional[int] = Field(None, description="估算的 token 数量")
    chunk_type: ChunkType = Field(default=ChunkType.TEXT, description="文本块类型")
    embedding: Optional[List[float]] = Field(None, description="向量表示")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外元数据")
    
    @field_validator('content')
    @classmethod
    def content_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Content cannot be empty')
        return v

    @field_validator('start_char', 'end_char')
    @classmethod
    def positions_valid(cls, v):
        if v < 0:
            raise ValueError('Character positions must be non-negative')
        return v

    @field_validator('end_char')
    @classmethod
    def end_after_start(cls, v, info):
        if info.data.get('start_char') is not None and v <= info.data['start_char']:
            raise ValueError('end_char must be greater than start_char')
        return v
    
    def get_length(self) -> int:
        """获取文本块长度"""
        return self.end_char - self.start_char
    
    def get_preview(self, max_length: int = 100) -> str:
        """获取内容预览"""
        if len(self.content) <= max_length:
            return self.content
        return self.content[:max_length] + "..."

class Document(BaseModel):
    """文档模型"""
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), description="文档ID")
    title: str = Field(..., description="文档标题")
    content: str = Field(..., description="文档内容")
    file_type: str = Field(..., description="文件类型")
    size: int = Field(..., description="文件大小（字节）")
    language: Optional[str] = Field(None, description="文档语言")
    encoding: str = Field(default="utf-8", description="文档编码")
    chunks: Optional[List[TextChunk]] = Field(None, description="分割后的文本块")
    topic_id: Optional[str] = Field(None, description="关联的主题ID")
    created_at: Optional[datetime] = Field(default_factory=datetime.now, description="创建时间")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外元数据")
    
    @field_validator('content')
    @classmethod
    def content_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Document content cannot be empty')
        return v

    @field_validator('size')
    @classmethod
    def size_matches_content(cls, v, info):
        if info.data.get('content'):
            actual_size = len(info.data['content'].encode('utf-8'))
            if abs(v - actual_size) > 100:  # 允许一些误差
                raise ValueError(f'Size mismatch: declared {v}, actual {actual_size}')
        return v
    
    def get_word_count(self) -> int:
        """获取单词数量"""
        return len(self.content.split())
    
    def get_char_count(self) -> int:
        """获取字符数量"""
        return len(self.content)
    
    def is_supported_type(self) -> bool:
        """检查是否为支持的文件类型"""
        supported_types = ['txt', 'md', 'markdown', 'py', 'js', 'html', 'css']
        return self.file_type.lower() in supported_types

class SplitResult(BaseModel):
    """分割结果模型"""
    document_id: str = Field(..., description="文档ID")
    chunks: List[TextChunk] = Field(..., description="分割后的文本块")
    strategy_used: str = Field(..., description="使用的分割策略")
    total_chunks: int = Field(..., description="总块数")
    total_tokens: Optional[int] = Field(None, description="总 token 数")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")
    error: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外元数据")
    
    @validator('total_chunks')
    def chunks_count_matches(cls, v, values):
        if 'chunks' in values and v != len(values['chunks']):
            raise ValueError('total_chunks must match the actual number of chunks')
        return v
    
    def get_average_chunk_size(self) -> float:
        """获取平均块大小"""
        if not self.chunks:
            return 0.0
        return sum(chunk.get_length() for chunk in self.chunks) / len(self.chunks)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取分割统计信息"""
        if not self.chunks:
            return {}
        
        chunk_sizes = [chunk.get_length() for chunk in self.chunks]
        token_counts = [chunk.token_count for chunk in self.chunks if chunk.token_count]
        
        return {
            "total_chunks": self.total_chunks,
            "avg_chunk_size": sum(chunk_sizes) / len(chunk_sizes),
            "min_chunk_size": min(chunk_sizes),
            "max_chunk_size": max(chunk_sizes),
            "avg_token_count": sum(token_counts) / len(token_counts) if token_counts else None,
            "processing_time": self.processing_time,
            "strategy_used": self.strategy_used
        }

class BatchSplitRequest(BaseModel):
    """批量分割请求模型"""
    documents: List[Document] = Field(..., description="待分割的文档列表")
    strategy_name: Optional[str] = Field(None, description="指定的分割策略")
    config_override: Optional[Dict[str, Any]] = Field(None, description="配置覆盖")
    
    @validator('documents')
    def documents_not_empty(cls, v):
        if not v:
            raise ValueError('Documents list cannot be empty')
        return v

class BatchSplitResult(BaseModel):
    """批量分割结果模型"""
    results: List[SplitResult] = Field(..., description="分割结果列表")
    total_documents: int = Field(..., description="总文档数")
    successful_count: int = Field(..., description="成功处理数")
    failed_count: int = Field(..., description="失败处理数")
    total_processing_time: Optional[float] = Field(None, description="总处理时间")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    @validator('total_documents')
    def total_matches_results(cls, v, values):
        if 'results' in values and v != len(values['results']):
            raise ValueError('total_documents must match the number of results')
        return v
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_documents == 0:
            return 0.0
        return self.successful_count / self.total_documents
    
    def get_failed_documents(self) -> List[str]:
        """获取失败的文档ID列表"""
        return [result.document_id for result in self.results if result.error]
